import React from "react";
import { cn } from "@/lib/utils";
import {
  LucideIcon,
  Smartphone,
  Baby,
  Gamepad2,
  Palette,
  Apple,
  Car,
  Shirt,
  Milk,
  Archive,
  ShoppingBag,
  Home,
  LayoutGrid,
  Search,
  User,
  ShoppingCart,
} from "lucide-react";

// Helper function to get Lucide icon from string identifier
const getIconComponent = (iconIdentifier: string | LucideIcon): LucideIcon => {
  if (typeof iconIdentifier === "function") {
    return iconIdentifier;
  }

  const iconMap: { [key: string]: LucideIcon } = {
    smartphone: Smartphone,
    baby: Baby,
    gamepad: Gamepad2,
    palette: Palette,
    apple: Apple,
    car: Car,
    shirt: Shirt,
    milk: Milk,
    archive: Archive,
    shopping: ShoppingBag,
    home: Home,
    grid: LayoutGrid,
    search: Search,
    user: User,
    cart: ShoppingCart,
  };

  return iconMap[iconIdentifier.toLowerCase()] || ShoppingBag;
};

interface CategoryIconProps {
  icon?: string | LucideIcon;
  title: string;
  image?: string;
  className?: string;
  onClick?: () => void;
  cardStyle?: any;
}

export const CategoryIcon: React.FC<CategoryIconProps> = ({
  icon,
  title,
  image,
  className,
  onClick,
  cardStyle,
}) => {
  return (
    <div
      className={cn(
        "flex flex-col items-center gap-2 cursor-pointer",
        "hover:bg-muted/50 transition-colors p-2 rounded-lg",
        className
      )}
      style={{
        background: cardStyle?.background || "transparent",
        border: cardStyle?.border || "none",
        borderRadius: cardStyle?.borderRadius || "8px",
        padding: cardStyle?.padding || "8px",
        margin: cardStyle?.margin || "0px",
        boxShadow: cardStyle?.boxShadow || "",
      }}
      onClick={onClick}
    >
      <div className="w-14 h-14 bg-mobile-category-icon rounded-xl flex items-center justify-center shadow-sm border border-border/50 overflow-hidden">
        {image ? (
          <img
            src={image}
            alt={title}
            className="w-full h-full object-cover"
            onError={(e) => {
              // Fallback to icon if image fails to load
              e.currentTarget.style.display = "none";
              e.currentTarget.nextElementSibling?.classList.remove("hidden");
            }}
          />
        ) : null}
        <div
          className={cn(
            "flex items-center justify-center",
            image ? "hidden" : ""
          )}
        >
          {icon
            ? React.createElement(getIconComponent(icon), {
                size: 24,
                className: "text-gray-600",
              })
            : React.createElement(ShoppingBag, {
                size: 24,
                className: "text-gray-600",
              })}
        </div>
      </div>
      {cardStyle?.visibleLabel && (
        <span
          style={{
            color: cardStyle.labelStyle?.color || "black",
            fontSize: cardStyle.labelStyle?.fontSize || 12,
          }}
        >
          {title}
        </span>
      )}
    </div>
  );
};
